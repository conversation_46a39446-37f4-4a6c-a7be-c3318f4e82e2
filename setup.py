#!/usr/bin/env python3
"""
Script de configuración para el Asistente de Voz con Minimax
"""

import os
import sys
import subprocess

def check_python_version():
    """Verifica que la versión de Python sea compatible"""
    if sys.version_info < (3, 7):
        print("❌ Error: Se requiere Python 3.7 o superior")
        print(f"Versión actual: {sys.version}")
        return False
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} detectado")
    return True

def install_dependencies():
    """Instala las dependencias del proyecto"""
    print("📦 Instalando dependencias...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Dependencias instaladas correctamente")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error instalando dependencias: {e}")
        return False

def setup_environment():
    """Configura el archivo de variables de entorno"""
    env_file = ".env"
    env_example = ".env.example"
    
    if os.path.exists(env_file):
        print("⚠️  El archivo .env ya existe")
        response = input("¿Quieres sobrescribirlo? (y/N): ").lower()
        if response != 'y':
            print("📝 Mantiendo archivo .env existente")
            return True
    
    if not os.path.exists(env_example):
        print("❌ Error: No se encontró .env.example")
        return False
    
    # Copiar ejemplo a .env
    with open(env_example, 'r') as f:
        content = f.read()
    
    with open(env_file, 'w') as f:
        f.write(content)
    
    print("✅ Archivo .env creado")
    print("📝 Por favor, edita el archivo .env con tus credenciales de Minimax:")
    print("   - MINIMAX_API_KEY: Tu API key de Minimax")
    print("   - MINIMAX_GROUP_ID: Tu Group ID de Minimax")
    
    return True

def create_audio_directory():
    """Crea el directorio para archivos de audio"""
    audio_dir = "audio_output"
    if not os.path.exists(audio_dir):
        os.makedirs(audio_dir)
        print(f"✅ Directorio {audio_dir} creado")
    else:
        print(f"📁 Directorio {audio_dir} ya existe")

def test_microphone():
    """Prueba básica del micrófono"""
    print("🎤 Probando micrófono...")
    try:
        import speech_recognition as sr
        r = sr.Recognizer()
        mic = sr.Microphone()
        
        print("✅ Micrófono detectado correctamente")
        return True
    except Exception as e:
        print(f"❌ Error con el micrófono: {e}")
        print("💡 Asegúrate de que el micrófono esté conectado y funcionando")
        return False

def main():
    """Función principal de configuración"""
    print("🚀 Configurando Asistente de Voz con Minimax")
    print("=" * 50)
    
    # Verificar Python
    if not check_python_version():
        return False
    
    # Instalar dependencias
    if not install_dependencies():
        return False
    
    # Configurar entorno
    if not setup_environment():
        return False
    
    # Crear directorio de audio
    create_audio_directory()
    
    # Probar micrófono
    test_microphone()
    
    print("\n" + "=" * 50)
    print("✅ Configuración completada")
    print("\n📋 Próximos pasos:")
    print("1. Edita el archivo .env con tus credenciales de Minimax")
    print("2. Ejecuta: python main.py")
    print("\n🔗 Para obtener credenciales de Minimax:")
    print("   https://www.minimax.io/platform/")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
