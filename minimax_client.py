import requests
import json
import os
from typing import Optional, Dict, Any
from config import Config

class MinimaxClient:
    """Cliente para la API de Minimax Text-to-Audio"""
    
    def __init__(self):
        self.api_key = Config.MINIMAX_API_KEY
        self.group_id = Config.MINIMAX_GROUP_ID
        self.base_url = Config.MINIMAX_BASE_URL
        
        # Validar configuración
        Config.validate()
        
        # Headers para las requests
        self.headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }
    
    def text_to_speech(self, text: str, voice_id: Optional[str] = None) -> Optional[bytes]:
        """
        Convierte texto a audio usando la API de Minimax
        
        Args:
            text (str): Texto a convertir
            voice_id (str, optional): ID de la voz a usar
            
        Returns:
            bytes: Audio en formato MP3 o None si hay error
        """
        if not text.strip():
            print("Error: El texto está vacío")
            return None
            
        if len(text) > 5000:
            print("Error: El texto excede los 5000 caracteres permitidos")
            return None
        
        voice_id = voice_id or Config.VOICE_ID
        
        # Endpoint para T2A v2
        url = f"{self.base_url}/v1/t2a_v2"
        
        # Parámetros de la request
        payload = {
            "group_id": self.group_id,
            "text": text,
            "voice_id": voice_id,
            "audio_setting": {
                "format": Config.AUDIO_FORMAT,
                "sample_rate": Config.SAMPLE_RATE
            }
        }
        
        try:
            print(f"Enviando texto a Minimax: {text[:50]}...")
            response = requests.post(url, headers=self.headers, json=payload, timeout=30)
            
            if response.status_code == 200:
                # La respuesta puede ser JSON con URL de audio o directamente bytes
                try:
                    response_json = response.json()
                    if 'audio_file' in response_json:
                        # Si la respuesta contiene URL del archivo de audio
                        audio_url = response_json['audio_file']
                        audio_response = requests.get(audio_url)
                        return audio_response.content
                    elif 'data' in response_json:
                        # Si la respuesta contiene datos de audio en base64
                        import base64
                        return base64.b64decode(response_json['data'])
                except json.JSONDecodeError:
                    # Si la respuesta es directamente el archivo de audio
                    return response.content
                    
            else:
                print(f"Error en la API de Minimax: {response.status_code}")
                print(f"Respuesta: {response.text}")
                return None
                
        except requests.exceptions.RequestException as e:
            print(f"Error de conexión con Minimax: {e}")
            return None
        except Exception as e:
            print(f"Error inesperado: {e}")
            return None
    
    def get_available_voices(self) -> Optional[Dict[str, Any]]:
        """
        Obtiene la lista de voces disponibles
        
        Returns:
            dict: Lista de voces disponibles o None si hay error
        """
        url = f"{self.base_url}/v1/voices"
        
        try:
            response = requests.get(url, headers=self.headers, timeout=10)
            
            if response.status_code == 200:
                return response.json()
            else:
                print(f"Error obteniendo voces: {response.status_code}")
                return None
                
        except requests.exceptions.RequestException as e:
            print(f"Error de conexión: {e}")
            return None
