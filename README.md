# Asistente de Voz con Minimax TTS

Una aplicación de Python que crea un agente de voz conversacional usando la API de Minimax para text-to-speech (TTS) y reconocimiento de voz local.

## Características

- 🎤 **Reconocimiento de voz**: Escucha y procesa comandos de voz en español
- 🔊 **Síntesis de voz**: Convierte respuestas a audio usando la API de Minimax
- 💬 **Conversación natural**: Mantiene conversaciones básicas con el usuario
- 🎵 **Reproducción de audio**: Reproduce las respuestas generadas
- 📁 **Guardado opcional**: Opción para guardar archivos de audio generados

## Requisitos

- Python 3.7+
- Micrófono funcional
- Altavoces o auriculares
- Cuenta en Minimax.io con API key

## Instalación

1. **Clonar o descargar el proyecto**

2. **Instalar dependencias**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Configurar variables de entorno**:
   - Copia `.env.example` a `.env`
   - Edita `.env` con tus credenciales de Minimax:
   ```
   MINIMAX_API_KEY=tu_api_key_aqui
   MINIMAX_GROUP_ID=tu_group_id_aqui
   ```

4. **Obtener credenciales de Minimax**:
   - Regístrate en [Minimax.io](https://www.minimax.io/platform/)
   - Obtén tu API key y Group ID desde el dashboard

## Uso

1. **Ejecutar la aplicación**:
   ```bash
   python main.py
   ```

2. **Interactuar con el asistente**:
   - Habla claramente al micrófono cuando se te indique
   - El asistente procesará tu pregunta y responderá con voz
   - Di "adiós" o "hasta luego" para terminar la conversación

## Comandos de Voz Soportados

- **Saludos**: "hola", "buenos días"
- **Estado**: "cómo estás", "qué tal"
- **Tiempo**: "qué hora es", "qué fecha es hoy"
- **Ayuda**: "ayuda", "qué puedes hacer"
- **Despedida**: "adiós", "hasta luego", "salir"

## Estructura del Proyecto

```
├── main.py              # Aplicación principal
├── minimax_client.py    # Cliente para API de Minimax
├── voice_agent.py       # Manejo de voz y audio
├── config.py           # Configuración
├── requirements.txt    # Dependencias
├── .env.example       # Ejemplo de variables de entorno
└── README.md          # Este archivo
```

## Configuración Avanzada

### Cambiar la voz

Edita `config.py` para cambiar el `VOICE_ID`:
```python
VOICE_ID = 'female-shaonv'  # Para voz femenina
```

### Ajustar timeouts de voz

Modifica en `config.py`:
```python
SPEECH_TIMEOUT = 10        # Tiempo máximo de espera
SPEECH_PHRASE_TIMEOUT = 3  # Tiempo máximo por frase
```

## Solución de Problemas

### Error de micrófono
- Verifica que el micrófono esté conectado y funcionando
- Asegúrate de dar permisos de micrófono a la aplicación

### Error de API de Minimax
- Verifica que tu API key y Group ID sean correctos
- Comprueba tu saldo en la cuenta de Minimax
- Revisa la conectividad a internet

### Error de reproducción de audio
- Verifica que los altavoces estén funcionando
- Instala pygame correctamente: `pip install pygame`

## Extensiones Futuras

- Integración con LLMs para respuestas más inteligentes
- Soporte para múltiples idiomas
- Interfaz gráfica
- Comandos de control del sistema
- Integración con APIs externas (clima, noticias, etc.)

## Licencia

Este proyecto es de código abierto. Úsalo y modifícalo según tus necesidades.
