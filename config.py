import os
from dotenv import load_dotenv

# Cargar variables de entorno
load_dotenv()

class Config:
    # API de Minimax
    MINIMAX_API_KEY = os.getenv('MINIMAX_API_KEY')
    MINIMAX_GROUP_ID = os.getenv('MINIMAX_GROUP_ID')
    MINIMAX_BASE_URL = 'https://api.minimax.io'
    
    # Configuración de voz
    VOICE_ID = 'male-qn-qingse'  # Voz por defecto
    AUDIO_FORMAT = 'mp3'
    SAMPLE_RATE = 22050
    
    # Configuración de reconocimiento de voz
    SPEECH_TIMEOUT = 5  # segundos
    SPEECH_PHRASE_TIMEOUT = 1  # segundos
    
    # Configuración de audio
    AUDIO_OUTPUT_DIR = 'audio_output'
    
    @classmethod
    def validate(cls):
        """Valida que las configuraciones requeridas estén presentes"""
        if not cls.MINIMAX_API_KEY:
            raise ValueError("MINIMAX_API_KEY no está configurada. Añádela al archivo .env")
        if not cls.MINIMAX_GROUP_ID:
            raise ValueError("MINIMAX_GROUP_ID no está configurada. Añádela al archivo .env")
