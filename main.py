#!/usr/bin/env python3
"""
Agente de Voz con Minimax TTS
Aplicación que escucha preguntas por voz y responde usando la API de Minimax
"""

import sys
import signal
from typing import Optional
from minimax_client import MinimaxClient
from voice_agent import VoiceAgent
from config import Config

class VoiceAssistant:
    """Asistente de voz principal que coordina reconocimiento y síntesis"""
    
    def __init__(self):
        print("Inicializando Asistente de Voz...")
        
        try:
            # Inicializar componentes
            self.minimax_client = MinimaxClient()
            self.voice_agent = VoiceAgent()
            
            # Estado de la aplicación
            self.running = False
            
            print("✅ Asistente de voz inicializado correctamente")
            
        except Exception as e:
            print(f"❌ Error inicializando asistente: {e}")
            sys.exit(1)
    
    def process_question(self, question: str) -> Optional[str]:
        """
        Procesa una pregunta y genera una respuesta
        
        Args:
            question (str): Pregunta del usuario
            
        Returns:
            str: Respuesta generada o None si hay error
        """
        # Por ahora, generar respuestas simples
        # En el futuro se puede integrar con un LLM para respuestas más inteligentes
        
        question_lower = question.lower()
        
        if "hola" in question_lower or "buenos días" in question_lower:
            return "¡Hola! Soy tu asistente de voz. ¿En qué puedo ayudarte hoy?"
        
        elif "cómo estás" in question_lower or "qué tal" in question_lower:
            return "Estoy muy bien, gracias por preguntar. ¿Y tú cómo estás?"
        
        elif "tiempo" in question_lower or "clima" in question_lower:
            return "Lo siento, no tengo acceso a información del clima en tiempo real. ¿Hay algo más en lo que pueda ayudarte?"
        
        elif "hora" in question_lower:
            import datetime
            now = datetime.datetime.now()
            return f"Son las {now.strftime('%H:%M')} del {now.strftime('%d de %B')}"
        
        elif "fecha" in question_lower:
            import datetime
            now = datetime.datetime.now()
            return f"Hoy es {now.strftime('%d de %B de %Y')}"
        
        elif "adiós" in question_lower or "hasta luego" in question_lower:
            return "¡Hasta luego! Ha sido un placer ayudarte."
        
        elif "ayuda" in question_lower:
            return "Puedo responder preguntas básicas, decirte la hora y fecha, y mantener una conversación contigo. ¿Qué te gustaría saber?"
        
        else:
            return f"Interesante pregunta: '{question}'. Soy un asistente básico, pero estoy aquí para ayudarte. ¿Podrías ser más específico?"
    
    def run_conversation_loop(self):
        """Ejecuta el loop principal de conversación"""
        self.running = True
        
        print("\n🎤 Asistente de Voz Activo")
        print("Comandos de voz:")
        print("- Di 'adiós' o 'hasta luego' para salir")
        print("- Presiona Ctrl+C para salir en cualquier momento")
        print("\n" + "="*50)
        
        # Mensaje de bienvenida
        welcome_message = "¡Hola! Soy tu asistente de voz. Puedes hacerme preguntas y te responderé."
        self._speak_response(welcome_message)
        
        while self.running:
            try:
                # Escuchar pregunta del usuario
                question = self.voice_agent.listen_for_speech()
                
                if question is None:
                    continue
                
                # Verificar comando de salida
                if any(word in question.lower() for word in ["adiós", "hasta luego", "salir", "terminar"]):
                    farewell = "¡Hasta luego! Ha sido un placer ayudarte."
                    self._speak_response(farewell)
                    break
                
                # Procesar pregunta y generar respuesta
                response = self.process_question(question)
                
                if response:
                    self._speak_response(response)
                else:
                    error_msg = "Lo siento, no pude procesar tu pregunta. ¿Podrías repetirla?"
                    self._speak_response(error_msg)
                
            except KeyboardInterrupt:
                print("\n\n🛑 Interrumpido por el usuario")
                break
            except Exception as e:
                print(f"❌ Error en el loop de conversación: {e}")
                continue
        
        self.running = False
        print("👋 Asistente de voz desactivado")
    
    def _speak_response(self, text: str):
        """
        Convierte texto a voz y lo reproduce
        
        Args:
            text (str): Texto a convertir a voz
        """
        print(f"🤖 Respuesta: {text}")
        
        # Convertir texto a audio usando Minimax
        audio_data = self.minimax_client.text_to_speech(text)
        
        if audio_data:
            # Reproducir audio
            success = self.voice_agent.play_audio(audio_data, save_file=False)
            if not success:
                print("❌ Error reproduciendo audio")
        else:
            print("❌ Error generando audio con Minimax")
    
    def cleanup(self):
        """Limpia recursos antes de salir"""
        print("🧹 Limpiando recursos...")
        self.voice_agent.cleanup()

def signal_handler(sig, frame):
    """Maneja la señal de interrupción (Ctrl+C)"""
    print("\n\n🛑 Recibida señal de interrupción")
    sys.exit(0)

def main():
    """Función principal"""
    # Configurar manejo de señales
    signal.signal(signal.SIGINT, signal_handler)
    
    # Crear y ejecutar asistente
    assistant = VoiceAssistant()
    
    try:
        assistant.run_conversation_loop()
    finally:
        assistant.cleanup()

if __name__ == "__main__":
    main()
