import speech_recognition as sr
import pygame
import io
import os
import tempfile
from typing import Optional
from config import Config

class VoiceAgent:
    """Agente de voz que maneja reconocimiento de voz y reproducción de audio"""
    
    def __init__(self):
        # Inicializar reconocedor de voz
        self.recognizer = sr.Recognizer()
        self.microphone = sr.Microphone()
        
        # Inicializar pygame para reproducción de audio
        pygame.mixer.init()
        
        # Crear directorio de salida de audio si no existe
        os.makedirs(Config.AUDIO_OUTPUT_DIR, exist_ok=True)
        
        # Calibrar el micrófono
        self._calibrate_microphone()
    
    def _calibrate_microphone(self):
        """Calibra el micrófono para el ruido ambiente"""
        print("Calibrando micrófono para ruido ambiente...")
        try:
            with self.microphone as source:
                self.recognizer.adjust_for_ambient_noise(source, duration=1)
            print("Calibración completada.")
        except Exception as e:
            print(f"Error en calibración: {e}")
    
    def listen_for_speech(self) -> Optional[str]:
        """
        Escucha y reconoce voz del usuario
        
        Returns:
            str: Texto reconocido o None si no se pudo reconocer
        """
        print("Escuchando... (habla ahora)")
        
        try:
            with self.microphone as source:
                # Escuchar audio
                audio = self.recognizer.listen(
                    source, 
                    timeout=Config.SPEECH_TIMEOUT,
                    phrase_time_limit=Config.SPEECH_PHRASE_TIMEOUT
                )
            
            print("Procesando audio...")
            
            # Reconocer usando Google Speech Recognition
            try:
                text = self.recognizer.recognize_google(audio, language='es-ES')
                print(f"Texto reconocido: {text}")
                return text
            except sr.UnknownValueError:
                print("No se pudo entender el audio")
                return None
            except sr.RequestError as e:
                print(f"Error en el servicio de reconocimiento: {e}")
                return None
                
        except sr.WaitTimeoutError:
            print("Tiempo de espera agotado")
            return None
        except Exception as e:
            print(f"Error inesperado en reconocimiento: {e}")
            return None
    
    def play_audio(self, audio_data: bytes, save_file: bool = False) -> bool:
        """
        Reproduce audio desde bytes
        
        Args:
            audio_data (bytes): Datos de audio en formato MP3
            save_file (bool): Si guardar el archivo de audio
            
        Returns:
            bool: True si se reprodujo correctamente, False en caso contrario
        """
        try:
            # Crear archivo temporal
            with tempfile.NamedTemporaryFile(suffix='.mp3', delete=False) as temp_file:
                temp_file.write(audio_data)
                temp_file_path = temp_file.name
            
            # Guardar archivo si se solicita
            if save_file:
                import time
                timestamp = int(time.time())
                save_path = os.path.join(Config.AUDIO_OUTPUT_DIR, f"response_{timestamp}.mp3")
                with open(save_path, 'wb') as f:
                    f.write(audio_data)
                print(f"Audio guardado en: {save_path}")
            
            # Reproducir audio
            print("Reproduciendo respuesta...")
            pygame.mixer.music.load(temp_file_path)
            pygame.mixer.music.play()
            
            # Esperar a que termine la reproducción
            while pygame.mixer.music.get_busy():
                pygame.time.wait(100)
            
            # Limpiar archivo temporal
            os.unlink(temp_file_path)
            
            return True
            
        except Exception as e:
            print(f"Error reproduciendo audio: {e}")
            return False
    
    def stop_audio(self):
        """Detiene la reproducción de audio"""
        pygame.mixer.music.stop()
    
    def cleanup(self):
        """Limpia recursos"""
        pygame.mixer.quit()
